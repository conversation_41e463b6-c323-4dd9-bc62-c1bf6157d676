// 图表工具函数

/**
 * 计算文本宽度（像素）
 * @param text 文本内容
 * @param fontSize 字体大小，默认12px
 * @param fontFamily 字体族，默认Arial
 * @returns 文本宽度（像素）
 */
export function getTextWidth(text: string, fontSize: number = 12, fontFamily: string = 'Arial'): number {
  // 创建临时canvas来测量文本宽度
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) return text.length * fontSize * 0.6; // fallback估算

  context.font = `${fontSize}px ${fontFamily}`;
  return context.measureText(text).width;
}

/**
 * 动态计算grid配置
 * @param data 图表数据
 * @param chartType 图表类型
 * @param options 配置选项
 * @returns grid配置对象
 */
export function calculateDynamicGrid(
  data: any,
  chartType: string,
  options: {
    fontSize?: number;
    fontFamily?: string;
    minLeft?: number;
    minRight?: number;
    minTop?: number;
    minBottom?: number;
    padding?: number;
  } = {},
) {
  const { fontSize = 12, fontFamily = 'Arial', minLeft = 40, minRight = 20, minTop = 25, minBottom = 30, padding = 10 } = options;

  let calculatedLeft = minLeft;
  let calculatedRight = minRight;
  let calculatedTop = minTop;
  let calculatedBottom = minBottom;

  // 根据图表类型和数据动态调整
  switch (chartType) {
    case 'bar':
    case 'line':
    case 'combo':
      // 纵向图表：检查Y轴标签长度
      if (data.yAxis && data.yAxis.data && Array.isArray(data.yAxis.data)) {
        const maxYLabelWidth = Math.max(...data.yAxis.data.map((label: string) => getTextWidth(String(label), fontSize, fontFamily)));
        calculatedLeft = Math.max(minLeft, maxYLabelWidth + padding);
      }

      // 检查X轴标签长度和旋转角度
      if (data.xAxis && data.xAxis.data && Array.isArray(data.xAxis.data)) {
        const maxXLabelWidth = Math.max(...data.xAxis.data.map((label: string) => getTextWidth(String(label), fontSize, fontFamily)));

        // 如果X轴标签旋转，需要更多底部空间
        const rotateAngle = data.xAxis.axisLabel?.rotate || 0;
        if (rotateAngle !== 0) {
          const rotatedHeight = Math.abs(Math.sin((rotateAngle * Math.PI) / 180)) * maxXLabelWidth;
          calculatedBottom = Math.max(minBottom, rotatedHeight + padding);
        } else if (maxXLabelWidth > 60) {
          // 标签过长时增加底部空间
          calculatedBottom = Math.max(minBottom, fontSize + padding * 2);
        }
      }
      break;

    case 'horizontal-bar':
      // 横向图表：检查Y轴标签长度（作为分类轴）
      if (data.yAxis && data.yAxis.data && Array.isArray(data.yAxis.data)) {
        const maxYLabelWidth = Math.max(...data.yAxis.data.map((label: string) => getTextWidth(String(label), fontSize, fontFamily)));
        calculatedLeft = Math.max(minLeft, maxYLabelWidth + padding);
      }

      // 检查X轴数值标签
      if (data.xAxis && data.xAxis.axisLabel) {
        calculatedBottom = Math.max(minBottom, fontSize + padding);
      }
      break;

    case 'pie':
      // 饼图：检查图例长度
      if (data.legend && data.legend.data && Array.isArray(data.legend.data)) {
        const maxLegendWidth = Math.max(...data.legend.data.map((label: string) => getTextWidth(String(label), fontSize, fontFamily)));

        // 根据图例位置调整
        const legendOrient = data.legend.orient || 'horizontal';
        const legendLeft = data.legend.left || 'center';

        if (legendOrient === 'vertical' && legendLeft === 'left') {
          calculatedLeft = Math.max(minLeft, maxLegendWidth + padding * 2);
        } else if (legendOrient === 'vertical' && legendLeft === 'right') {
          calculatedRight = Math.max(minRight, maxLegendWidth + padding * 2);
        }
      }
      break;

    case 'scatter':
      // 散点图：检查轴标签
      if (data.xAxis && data.xAxis.name) {
        calculatedBottom = Math.max(minBottom, fontSize * 2 + padding);
      }

      if (data.yAxis && data.yAxis.name) {
        calculatedLeft = Math.max(minLeft, fontSize * 2 + padding);
      }
      break;

    case 'funnel':
      // 漏斗图：检查标签长度
      if (data.series && data.series[0] && data.series[0].data) {
        const maxLabelWidth = Math.max(
          ...data.series[0].data.map((item: any) => getTextWidth(String(item.name || ''), fontSize, fontFamily)),
        );
        calculatedLeft = Math.max(minLeft, maxLabelWidth / 2 + padding);
        calculatedRight = Math.max(minRight, maxLabelWidth / 2 + padding);
      }
      break;
  }

  return {
    top: calculatedTop,
    bottom: calculatedBottom,
    left: calculatedLeft,
    right: calculatedRight,
    containLabel: true,
  };
}

/**
 * 应用动态grid到图表配置
 * @param chartOption 图表配置
 * @param chartType 图表类型
 * @param customOptions 自定义选项
 * @returns 更新后的图表配置
 */
export function applyDynamicGrid(chartOption: any, chartType: string, customOptions?: any): any {
  const dynamicGrid = calculateDynamicGrid(chartOption, chartType, customOptions);

  return {
    ...chartOption,
    grid: {
      ...chartOption.grid,
      ...dynamicGrid,
    },
  };
}

/**
 * 检查文本是否会溢出
 * @param text 文本内容
 * @param maxWidth 最大宽度
 * @param fontSize 字体大小
 * @param fontFamily 字体族
 * @returns 是否溢出
 */
export function isTextOverflow(text: string, maxWidth: number, fontSize: number = 12, fontFamily: string = 'Arial'): boolean {
  const textWidth = getTextWidth(text, fontSize, fontFamily);
  return textWidth > maxWidth;
}

/**
 * 截断过长的文本并添加省略号
 * @param text 原始文本
 * @param maxWidth 最大宽度
 * @param fontSize 字体大小
 * @param fontFamily 字体族
 * @returns 处理后的文本
 */
export function truncateText(text: string, maxWidth: number, fontSize: number = 12, fontFamily: string = 'Arial'): string {
  if (!isTextOverflow(text, maxWidth, fontSize, fontFamily)) {
    return text;
  }

  const ellipsis = '...';
  const ellipsisWidth = getTextWidth(ellipsis, fontSize, fontFamily);
  const availableWidth = maxWidth - ellipsisWidth;

  let truncated = '';
  for (let i = 0; i < text.length; i++) {
    const testText = text.substring(0, i + 1);
    if (getTextWidth(testText, fontSize, fontFamily) > availableWidth) {
      break;
    }
    truncated = testText;
  }

  return truncated + ellipsis;
}
